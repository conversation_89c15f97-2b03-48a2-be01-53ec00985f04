package com.qmqb.imp.test;

import com.qmqb.imp.job.indicator.PerformanceFeedbackManager;
import com.qmqb.imp.job.indicator.PerformanceGenerator;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 绩效反馈定时任务测试类
 * <p>用于测试定时任务相关的绩效反馈生成逻辑</p>
 * <AUTHOR>
 * @since 2025-07-05
 */
@SpringBootTest
@DisplayName("绩效反馈定时任务测试")
public class PerformanceFeedbackJobTest {

    @Autowired
    private PerformanceFeedbackManager performanceFeedbackManager;

    @Autowired
    private PerformanceGenerator performanceGenerator;

    /**
     * 测试生成指定年月和部门的绩效反馈记录
     */
    @Test
    @DisplayName("生成指定年月和部门的绩效反馈记录")
    public void testGenerateFeedbackByYearMonthAndDept() {
        performanceFeedbackManager.generateFeedbackByYearMonthAndDept(2025, 6, 1, null);
    }

    /**
     * 测试生成指定年月和部门的绩效反馈记录
     */
    @Test
    @DisplayName("生成指定年月和部门的绩效等级")
    public void testgeneratefeedbackbyyearmonthanddept() {
        performanceGenerator.generatePerformanceFromFeedbackAndDept(2025, 6, 0, null);
    }
}
