package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.system.domain.PerformanceRegistration;
import com.qmqb.imp.system.domain.bo.PerformanceRegistrationBo;
import com.qmqb.imp.system.domain.vo.PerformanceRegistrationVo;
import com.qmqb.imp.system.mapper.PerformanceRegistrationMapper;
import com.qmqb.imp.system.domain.vo.PerformanceStatisticVO;
import com.qmqb.imp.system.service.IPerformanceRegistrationService;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 绩效登记Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@RequiredArgsConstructor
@Service
public class PerformanceRegistrationServiceImpl implements IPerformanceRegistrationService {

    private final PerformanceRegistrationMapper baseMapper;
    private final ISysUserService sysUserService;
    private final ISysDeptService sysDeptService;

    /**
     * 查询绩效登记
     */
    @Override
    public PerformanceRegistrationVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询绩效登记列表
     */
    @Override
    public TableDataInfo<PerformanceRegistrationVo> queryPageList(PerformanceRegistrationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PerformanceRegistration> lqw = buildQueryWrapper(bo);
        IPage<PerformanceRegistration> build = pageQuery.build();
        for (OrderItem order : build.orders()) {
            if("level".equals(order.getColumn())){
                order.setColumn("FIELD(level, 'D', 'C', 'B', 'A', 'S')");
            }
        }
        if (bo.getType() != null && bo.getType() == 1) {
            lqw.ne(PerformanceRegistration::getLevel, "D");
        }
        Page<PerformanceRegistrationVo> result = baseMapper.selectVoPage(build, lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<PerformanceStatisticVO> getPerformanceStatistic(PerformanceRegistrationBo bo, PageQuery pageQuery) {
        IPage<PerformanceStatisticVO> performanceStatistic = baseMapper.getPerformanceStatistic(pageQuery.build(), bo);
        return TableDataInfo.build(performanceStatistic);
    }

    /**
     * 查询绩效登记列表
     */
    @Override
    public List<PerformanceRegistrationVo> queryList(PerformanceRegistrationBo bo) {
        LambdaQueryWrapper<PerformanceRegistration> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PerformanceRegistration> buildQueryWrapper(PerformanceRegistrationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PerformanceRegistration> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDeptId() != null, PerformanceRegistration::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), PerformanceRegistration::getDeptName, bo.getDeptName());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), PerformanceRegistration::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getEvalYear()), PerformanceRegistration::getEvalYear, bo.getEvalYear());
        if (!Constants.MINUS_ONE.equals(bo.getEvalMonth())) {
            lqw.eq(StringUtils.isNotBlank(bo.getEvalMonth()), PerformanceRegistration::getEvalMonth, bo.getEvalMonth());
        }
        lqw.eq(bo.getLevel() != null, PerformanceRegistration::getLevel, bo.getLevel());
        return lqw;
    }

    /**
     * 新增绩效登记
     */
    @Override
    public Boolean insertByBo(PerformanceRegistrationBo bo) {

        List<String> userNames = new ArrayList<>();
        List<String> performanceByS = Arrays.asList(bo.getPerformanceByS().trim().split("\\s+"));
        List<String> performanceByA = Arrays.asList(bo.getPerformanceByA().trim().split("\\s+"));
        List<String> performanceByB = new ArrayList<>();
        List<String> performanceByC = new ArrayList<>();
        List<String> performanceByD = new ArrayList<>();
        userNames.addAll(performanceByS);
        userNames.addAll(performanceByA);
        if(StringUtils.isNotBlank(bo.getPerformanceByB())) {
            performanceByB = Arrays.asList(bo.getPerformanceByB().trim().split("\\s+"));
            userNames.addAll(performanceByB);
        }
        if(StringUtils.isNotBlank(bo.getPerformanceByC())) {
            performanceByC = Arrays.asList(bo.getPerformanceByC().trim().split("\\s+"));
            userNames.addAll(performanceByC);
        }
        if(StringUtils.isNotBlank(bo.getPerformanceByD())) {
            performanceByD = Arrays.asList(bo.getPerformanceByD().trim().split("\\s+"));
            userNames.addAll(performanceByD);
        }
        //离职判断
        List<SysUser> sysUsers = sysUserService.selectUserByNickNames(userNames);
        List<String> existsSysUserNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        List<String> noExistUser = new ArrayList<>();
        //找出不存在的用户
        userNames.forEach(tmp->{
            if(!existsSysUserNames.contains(tmp)) {
                noExistUser.add(tmp);
            }
        });

        Set<Long> deptIds = sysUsers.stream().map(SysUser::getDeptId).collect(Collectors.toSet());
        List<SysDept> enableDepts = sysDeptService.listByDeptIds(deptIds);
        List<String> noExistDeptUserNames = new ArrayList<>();
        if(CollectionUtil.isEmpty(enableDepts)) {
            throw new ServiceException("所有填写的姓名找不到对应的组，保存失败");
        }
        Set<Long> enableDeptIds = enableDepts.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
        sysUsers.forEach(tmp->{
            //找出没有对应的组的用户名
            if(!enableDeptIds.contains(tmp.getDeptId())) {
                noExistDeptUserNames.add(tmp.getNickName());
            }
        });
        if(CollectionUtil.isNotEmpty(noExistDeptUserNames)) {
            throw new ServiceException(noExistDeptUserNames+ "找不到对应的组，保存失败");
        }

        Map<Long, SysDept> enableDeptsMap = enableDepts.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        List<PerformanceRegistration> insertBatch = new ArrayList<>();
        List<String> finalPerformanceByB = performanceByB;
        List<String> finalPerformanceByC = performanceByC;
        List<String> finalPerformanceByD = performanceByD;
        sysUsers.forEach(tmp->{
            String level;
            if(performanceByS.contains(tmp.getNickName())) {
                level = "S";
            }else if(performanceByA.contains(tmp.getNickName())) {
                level = "A";
            }else if(finalPerformanceByB.contains(tmp.getNickName())) {
                level = "B";
            }else if(finalPerformanceByC.contains(tmp.getNickName())) {
                level = "C";
            }else if(finalPerformanceByD.contains(tmp.getNickName())) {
                level = "D";
            }else {
                throw new ServiceException(tmp.getNickName()+ "找不到对应的绩效级别，保存失败");
            }
            PerformanceRegistration performanceRegistration = PerformanceRegistration.builder().userName(tmp.getNickName()).deptId(tmp.getDeptId())
                .deptName(enableDeptsMap.get(tmp.getDeptId()).getDeptName()).level(level).evalYear(bo.getEvalYear()).evalMonth(bo.getEvalMonth()).build();
            insertBatch.add(performanceRegistration);
        });

        return baseMapper.insertBatch(insertBatch);
    }

    /**
     * 修改绩效登记
     */
    @Override
    public Boolean updateByBo(PerformanceRegistrationBo bo) {
        PerformanceRegistration update = BeanUtil.toBean(bo, PerformanceRegistration.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PerformanceRegistration entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除绩效登记
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
