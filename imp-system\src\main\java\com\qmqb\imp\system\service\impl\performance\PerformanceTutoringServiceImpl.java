package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.imp.common.enums.IndicatorCategoryEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.bo.performance.PerformanceTutoringBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceTutoringQueryBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.domain.vo.performance.PerformanceTutoringVo;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceTutoringMapper;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import com.qmqb.imp.system.service.indicator.IPerformanceTutoringService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 绩效辅导Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class PerformanceTutoringServiceImpl implements IPerformanceTutoringService {

    private final PerformanceTutoringMapper baseMapper;
    private final ISysUserService userService;
    private final IPerformanceFeedbackService performanceFeedbackService;
    private final PerformanceFeedbackMainMapper performanceFeedbackMainMapper;

    /**
     * 查询绩效辅导
     */
    @Override
    public PerformanceTutoringVo queryById(Long id){
        PerformanceTutoringVo performanceTutoringVo = baseMapper.selectVoById(id);
        PerformanceFeedback performanceFeedback = performanceFeedbackService.getById(performanceTutoringVo.getFeedbackId());
        if (performanceFeedback == null) {
            throw new ServiceException("绩效反馈不存在!");
        }
        PerformanceFeedbackMain performanceFeedbackMain = performanceFeedbackMainMapper.selectById(performanceFeedback.getMainFeedbackId());
        if (performanceFeedbackMain == null) {
            throw new ServiceException("绩效反馈主表不存在!");
        }
        performanceTutoringVo.setPrimaryIndicator(performanceFeedback.getPrimaryIndicator());
        performanceTutoringVo.setPrimaryIndicatorName(IndicatorCategoryEnum.getByCode(performanceFeedback.getPrimaryIndicator()).getName());
        performanceTutoringVo.setSecondaryIndicator(performanceFeedback.getSecondaryIndicator());
        performanceTutoringVo.setSecondaryIndicatorName(PerformanceIndicatorEnum.fromCode(performanceFeedback.getSecondaryIndicator()).getName());
        performanceTutoringVo.setEventTitle(performanceFeedback.getEventTitle());
        performanceTutoringVo.setEventStartTime(performanceFeedbackMain.getEventStartTime());
        performanceTutoringVo.setEventEndTime(performanceFeedbackMain.getEventEndTime());
        performanceTutoringVo.setGroupName(performanceFeedback.getGroupName());
        PersonTypeEnum personTypeEnum = PersonTypeEnum.fromType(Integer.valueOf(performanceFeedback.getPersonType()));
        performanceTutoringVo.setRoleName(personTypeEnum.getDesc());
        performanceTutoringVo.setNickName(performanceFeedback.getNickName());
        performanceTutoringVo.setRecommendedLevel(performanceFeedback.getRecommendedLevel());
        return performanceTutoringVo;
    }

    /**
     * 查询绩效辅导列表
     */
    @Override
    public TableDataInfo<PerformanceTutoringVo> queryPageList(PerformanceTutoringQueryBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser sysUser = userService.selectUserById(loginUser.getUserId());
        if (!sysUser.isAdmin() && !sysUser.isJszxAdmin()) {
            if (ObjectUtils.isNotEmpty(bo.getGroupId()) && !Objects.equals(bo.getGroupId(), sysUser.getDeptId())) {
                return TableDataInfo.build(new Page<>());
            }
            bo.setGroupId(sysUser.getDeptId());
            if (StringUtils.isNotBlank(bo.getNickName())) {
                List<String> nicknameList = userService.selectUserByDeptId(loginUser.getDeptId()).stream()
                    .collect(Collectors.mapping(user -> user.getNickName(), Collectors.toList()));
                if (!nicknameList.contains(bo.getNickName())) {
                    return TableDataInfo.build(new Page<>());
                }
            }
        }
        Page<PerformanceTutoringVo> result = baseMapper.selectTutoringPage(pageQuery.build(), bo);

        if (result.getRecords() != null) {
            result.getRecords().stream().forEach(item -> {
                item.setPrimaryIndicatorName(IndicatorCategoryEnum.getByCode(item.getPrimaryIndicator()).getName());
                item.setSecondaryIndicatorName(PerformanceIndicatorEnum.fromCode(item.getSecondaryIndicator()).getName());
            });
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询绩效辅导列表
     */
    @Override
    public List<PerformanceTutoringVo> queryList(PerformanceTutoringBo bo) {
        LambdaQueryWrapper<PerformanceTutoring> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PerformanceTutoring> buildQueryWrapper(PerformanceTutoringBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PerformanceTutoring> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getFeedbackId() != null, PerformanceTutoring::getFeedbackId, bo.getFeedbackId());
        lqw.eq(StringUtils.isNotBlank(bo.getTutoringSummary()), PerformanceTutoring::getTutoringSummary, bo.getTutoringSummary());
        lqw.eq(StringUtils.isNotBlank(bo.getTutoringResult()), PerformanceTutoring::getTutoringResult, bo.getTutoringResult());
        lqw.eq(StringUtils.isNotBlank(bo.getTutor()), PerformanceTutoring::getTutor, bo.getTutor());
        lqw.eq(bo.getTutoringTime() != null, PerformanceTutoring::getTutoringTime, bo.getTutoringTime());
        lqw.eq(StringUtils.isNotBlank(bo.getTutoringAttachment()), PerformanceTutoring::getTutoringAttachment, bo.getTutoringAttachment());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectorSuggest()), PerformanceTutoring::getDirectorSuggest, bo.getDirectorSuggest());
        lqw.eq(bo.getSuggestTime() != null, PerformanceTutoring::getSuggestTime, bo.getSuggestTime());
        return lqw;
    }

    /**
     * 新增绩效辅导
     */
    @Override
    public Boolean insertByBo(PerformanceTutoringBo bo) {
        PerformanceTutoring add = BeanUtil.toBean(bo, PerformanceTutoring.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    @Override
    public Boolean tutoring(PerformanceTutoringBo bo) {
        PerformanceTutoring updateBo =  BeanUtil.toBean(bo, PerformanceTutoring.class);
        baseMapper.updateById(updateBo);
        return true;
    }

    @Override
    public Boolean suggest(PerformanceTutoringBo bo) {
        PerformanceTutoring updateBo =  BeanUtil.toBean(bo, PerformanceTutoring.class);
        updateBo.setSuggestTime(new Date());
        baseMapper.updateById(updateBo);
        return true;
    }

    @Override
    public Boolean saveBatch(List<PerformanceTutoring> tutoringList) {
        return baseMapper.insertBatch(tutoringList);
    }

    @Override
    public void removeByMainIds(List<String> mainIds) {
        if (mainIds == null || mainIds.isEmpty()) {
            return;
        }
        List<Long> feedbackIds =  performanceFeedbackService.lambdaQuery()
            .in(PerformanceFeedback::getMainFeedbackId, mainIds).list()
            .stream().map(PerformanceFeedback::getId).collect(Collectors.toList());
        if (feedbackIds == null || feedbackIds.isEmpty()) {
            return;
        }
        baseMapper.delete(new LambdaQueryWrapper<PerformanceTutoring>()
            .in(PerformanceTutoring::getFeedbackId, feedbackIds));
    }


}
