<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.PerformanceRegistrationMapper">

    <resultMap type="com.qmqb.imp.system.domain.PerformanceRegistration" id="PerformanceRegistrationResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="userName" column="user_name"/>
        <result property="evalYear" column="eval_year"/>
        <result property="evalMonth" column="eval_month"/>
        <result property="level" column="level"/>
        <result property="delFlag" column="del_flag"/>
        <result property="version" column="version"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO tb_performance_registration (
        id,
        dept_id,
        dept_name,
        user_name,
        eval_year,
        eval_month,
        level,
        del_flag,
        version,
        create_by,
        create_time,
        update_by,
        update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.deptId},
            #{item.deptName},
            #{item.userName},
            #{item.evalYear},
            #{item.evalMonth},
            #{item.level},
            #{item.delFlag, jdbcType=CHAR},
            #{item.version},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        dept_id = VALUES(dept_id),
        dept_name = VALUES(dept_name),
        level = VALUES(level),
        del_flag = VALUES(del_flag),
        version = VALUES(version) + 1,
        update_by = VALUES(update_by),
        update_time = VALUES(update_time)
    </insert>

    <select id="getPerformanceStatistic" resultType="com.qmqb.imp.system.domain.vo.PerformanceStatisticVO">
        SELECT
        dept_name,
        user_name,
        SUM(CASE WHEN level = 'S' THEN 1 ELSE 0 END) AS s_count,
        SUM(CASE WHEN level = 'A' THEN 1 ELSE 0 END) AS a_count,
        SUM(CASE WHEN level = 'C' THEN 1 ELSE 0 END) AS c_count,
        COUNT(*) AS total_count
        FROM tb_performance_registration
        WHERE del_flag = '0'
        <if test="bo.evalYear != null and bo.evalYear != ''">
            AND eval_year = #{bo.evalYear}
        </if>
        <if test="bo.evalMonth != null and bo.evalMonth != ''">
            AND eval_month = #{bo.evalMonth}
        </if>
        <if test="bo.userName != null and bo.userName != ''">
            AND user_name LIKE CONCAT('%', #{bo.userName}, '%')
        </if>
        GROUP BY dept_name, user_name
    </select>

</mapper>
