package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.*;

import javax.validation.constraints.*;


/**
 * 绩效登记业务对象 tb_performance_registration
 *
 * <AUTHOR>
 * @date 2023-05-23
 */

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PerformanceRegistrationBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 部门ID
     */
    @NotNull(message = "部门名称不能为空", groups = { EditGroup.class })
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { EditGroup.class })
    private String userName;

    /**
     * 评定年
     */
    @NotBlank(message = "评定年不能为空", groups = { AddGroup.class, EditGroup.class })
    private String evalYear;

    /**
     * 评定月
     */
    @NotBlank(message = "评定月不能为空", groups = { AddGroup.class, EditGroup.class })
    private String evalMonth;

    /**
     * 绩效级别 0: S , 1: A , 2: B , 3:C 4:D
     */
    private String level;

    /**
     * 绩效S
     */
    @NotBlank(message = "绩效S姓名不能为空", groups = { AddGroup.class })
    private String performanceByS;
    /**
     * 绩效A
     */
    @NotBlank(message = "绩效A姓名不能为空", groups = { AddGroup.class })
    private String performanceByA;
    /**
     * 绩效B
     */
    private String performanceByB;
    /**
     * 绩效C
     */
    private String performanceByC;
    /**
     * 绩效C
     */
    private String performanceByD;

    /**
     * 1为优秀查询页面，0为优秀绩效登记页面
     */
    private Integer type;

}
