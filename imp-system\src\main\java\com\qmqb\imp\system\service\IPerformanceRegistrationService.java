package com.qmqb.imp.system.service;

import com.qmqb.imp.system.domain.vo.PerformanceRegistrationVo;
import com.qmqb.imp.system.domain.bo.PerformanceRegistrationBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.PerformanceStatisticVO;

import java.util.Collection;
import java.util.List;

/**
 * 绩效登记Service接口
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
public interface IPerformanceRegistrationService {

    /**
     * 查询绩效登记
     *
     * @param id 绩效登记主键
     * @return 绩效登记信息
     */
    PerformanceRegistrationVo queryById(Long id);

    /**
     * 查询绩效登记列表
     *
     * @param bo        绩效登记业务对象
     * @param pageQuery 分页查询对象
     * @return 绩效登记分页数据
     */
    TableDataInfo<PerformanceRegistrationVo> queryPageList(PerformanceRegistrationBo bo, PageQuery pageQuery);

    /**
     * 查询绩效统计明细列表
     *
     * @param bo        绩效登记业务对象
     * @param pageQuery 分页查询对象
     * @return 绩效统计明细分页数据
     */
    TableDataInfo<PerformanceStatisticVO> getPerformanceStatistic(PerformanceRegistrationBo bo, PageQuery pageQuery);

    /**
     * 查询绩效登记列表
     *
     * @param bo 绩效登记业务对象
     * @return 绩效登记列表
     */
    List<PerformanceRegistrationVo> queryList(PerformanceRegistrationBo bo);

    /**
     * 新增绩效登记
     *
     * @param bo 绩效登记业务对象
     * @return 结果
     */
    Boolean insertByBo(PerformanceRegistrationBo bo);

    /**
     * 修改绩效登记
     *
     * @param bo 绩效登记业务对象
     * @return 结果
     */
    Boolean updateByBo(PerformanceRegistrationBo bo);

    /**
     * 校验并批量删除绩效登记信息
     *
     * @param ids     需要删除的绩效登记ID集合
     * @param isValid 是否进行有效性校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
