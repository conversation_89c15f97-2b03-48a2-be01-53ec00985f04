<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.performance.PerformanceEventMapper">

    <resultMap type="com.qmqb.imp.system.domain.performance.PerformanceEvent" id="PerformanceEventResult">
        <result property="id" column="id"/>
        <result property="feedbackCode" column="feedback_code"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="feedbackTime" column="feedback_time"/>
        <result property="eventTitle" column="event_title"/>
        <result property="eventDetail" column="event_detail"/>
        <result property="eventStartTime" column="event_start_time"/>
        <result property="eventEndTime" column="event_end_time"/>
        <result property="dataSource" column="data_source"/>
        <result property="submitStatus" column="submit_status"/>
        <result property="submitTime" column="submit_time"/>
        <result property="submitter" column="submitter"/>
        <result property="projectManagerAuditStatus" column="project_manager_audit_status"/>
        <result property="finalAudit" column="final_audit"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getMaxMainFeedbackCodeByDatePrefix" resultType="java.lang.String">
        SELECT MAX(feedback_code)
        FROM tb_performance_event
        WHERE feedback_code LIKE CONCAT(#{datePrefix}, '%')
    </select>

    <select id="selectPerformanceDistributionByEventIds" resultType="com.qmqb.imp.system.domain.dto.PerformanceDistributionDTO">
        SELECT
            m.event_id AS eventId,
            SUM(CASE WHEN f.recommended_level = 'S' THEN 1 ELSE 0 END) AS sCount,
            SUM(CASE WHEN f.recommended_level = 'A' THEN 1 ELSE 0 END) AS aCount,
            SUM(CASE WHEN f.recommended_level = 'C' THEN 1 ELSE 0 END) AS cCount,
            SUM(CASE WHEN f.recommended_level = 'D' THEN 1 ELSE 0 END) AS dCount
        FROM tb_performance_feedback_main m
        LEFT JOIN tb_performance_feedback f ON m.id = f.main_feedback_id
        <where>
            <if test="eventIds != null and eventIds.size > 0">
                m.event_id IN
                <foreach collection="eventIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY m.event_id
    </select>

    <select id="selectEventStatusByEventIds" resultType="com.qmqb.imp.system.domain.dto.PerformanceEventStatusDTO">
        SELECT
            m.event_id AS eventId,
            CASE
                WHEN SUM(m.submit_status = 'PENDING_RESUBMIT') > 0 THEN 'PENDING_RESUBMIT'
                WHEN SUM(m.submit_status = 'NOT_SUBMITTED') = COUNT(1) THEN 'NOT_SUBMITTED'
                WHEN SUM(m.submit_status = 'SUBMITTED') = COUNT(1) THEN 'SUBMITTED'
                ELSE 'PART_SUBMITTED'
            END AS submitStatus,
            CASE
                WHEN SUM(m.project_manager_audit_status = 'APPROVED' OR m.project_manager_audit_status = 'REJECTED') > 0 THEN 'AUDITED'
                ELSE 'NOT_AUDITED'
            END AS projectManagerAuditStatus,
            CASE
                WHEN SUM(m.final_audit = 'APPROVED' OR m.final_audit = 'REJECTED') > 0 THEN 'AUDITED'
                ELSE 'NOT_AUDITED'
            END AS finalAudit
        FROM tb_performance_feedback_main m
        <where>
            <if test="eventIds != null and eventIds.size > 0">
                m.event_id IN
                <foreach collection="eventIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY m.event_id
    </select>

</mapper>
