package com.qmqb.imp.system.mapper;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.system.domain.PerformanceRegistration;
import com.qmqb.imp.system.domain.bo.PerformanceRegistrationBo;
import com.qmqb.imp.system.domain.vo.PerformanceRegistrationVo;

import java.util.List;
import com.qmqb.imp.system.domain.vo.PerformanceStatisticVO;
import org.apache.ibatis.annotations.Param;

/**
 * 绩效登记Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
public interface PerformanceRegistrationMapper extends BaseMapperPlus<PerformanceRegistrationMapper, PerformanceRegistration, PerformanceRegistrationVo> {


    /**
     * 批量更新活插入绩效登记
     *
     * @param list
     * @return
     */
    boolean insertOrUpdateBatch(List<PerformanceRegistration> list);


    /**
     *  绩效结果统计
     * <AUTHOR>
     * @param page
     * @param bo
     * @return
     * @date 2025/7/9 11:39
     */
    IPage<PerformanceStatisticVO> getPerformanceStatistic (Page<PerformanceStatisticVO> page, @Param("bo") PerformanceRegistrationBo bo);
}
