> [分支规范](https://pm.qmqb.top/doc-objectLibs-custom-0-894-5226.html)<br/>
> [版本规范](https://pm.qmqb.top/doc-objectLibs-custom-0-894-5227.html)<br/>

### v1.0.0.0.20241119
    1.fix:修改考勤预警定时器重复数据Bug

### v1.0.0.1.20241125
    1.fix:git代码提交统计表定时任务-修改跳过非技术中心用户

### v1.0.0.2.20241125
    1.fix:git代码提交统计表定时任务-修改跳过非技术中心用户

### v1.0.0.3.20241202
    1.feat：绩效系统二期迭代上线

### v1.0.0.4.20241203
    1.fix:绩效系统二期修改部门下拉框权限问题
    2.config:修改gitlab地址

### v1.0.0.5.20241203
    1.feat:禅道任务统计优化成定时任务
    2.fix:修改查看部门数据权限问题

### v1.1.0.0.20241220
    1.三期迭代优化需求

### v1.1.0.1.20241220
    1.修改拉取代码路径

### v1.1.0.2.20241223
    1.处理考勤预警异常
    2.删除钉钉token缓存
    3.首页任务趋势图需求中逻辑修改

### v1.1.0.3.20241225
    1.代码扫描不扫码废弃的代码库
    2.合并扫描报告页相同路径和数量

### v1.1.0.4.20241227
    1.fix:处理代码扫描增量扫描空指针异常

### v1.1.0.5.20250107
    1.钉钉审批流程数据统计汇总到绩效管理系统

### v1.1.0.6.20250121
    1.增加分支数量统计接口和代码严重错误的数量统计接口 
    2.调整zt_bug字段

### v1.1.0.7.20250122
    1.修改文档跳转地址

### v1.1.0.8.20250123
    1.任务没更新预警同步新版禅道规则
    2.工作管理-需求统计列表报错调整

### v1.1.0.9.20250124
    1.任务没更新预警同步新版禅道规则

### v1.1.0.10.20250208
    1.fix:处理代码扫描已修改的问题没有标识为已处理

### v1.1.0.11.20250212
    1.fix：fix：文档表字段报错修复

### v1.1.0.12.20250218
    1.fix：【工作管理】任务查询页面逻辑调整
    2.fix：【工作管理】任务统计图表逻辑调整
    3.fix：【首页】全年任务趋势图逻辑调整

### v1.1.0.13.20250303
    1.新增bug详情和bug统计

### v1.1.0.14.20250306
    1.新增bug详情和bug统计

### v1.1.0.15.20250314
    1.生产应用每周巡检流程添加年份,月份,周次查询条件

### v1.1.0.16.20250317
    1.过滤禅道标题含有生产的bug

### v1.1.0.17.20250319
    1.流程统计

### v1.1.0.18.20250320
    1.流程统计添加最后更新时间

### v1.1.0.19.20250321
    1.流程统计优化统计已完成/已通过

### v1.1.0.20.20250321

    1.流程统计不计算抄送人为操作人

### v1.1.0.21.20250322
    1.fix:bug统计产品名称和项目名称错误

### v1.1.0.22.20250407
    1.feat:值班提醒优化

### v1.1.0.23.20250416

    1.feat:全部预警添加导出功能

### v1.1.0.24.20250418
    1.feat:值班提醒优化

### v1.1.0.25.20250418
    1.feat:用户管理新增入职时间
    2.feat:预警过滤入职时间没满10天的用户

### v1.1.0.26.20250422
    1.feat:bug统计导出

### v1.1.0.27.20250424
    1.feat:工作成果跟踪页面查全部时月份数据合并

### v1.1.0.28.20250427
    1.feat:增加工作明细

### v1.1.0.29.20250428
    1.feat:个人任务明细新增导出功能

### v1.1.0.30.20250429
    1.feat:新增业务预警功能模块

### v1.1.0.31.20250506
    1.feat:新增工时预警定时任务

### v1.1.0.32.2025050602
    1.feat:bug详情+bug统计菜单优化 

### v1.1.0.33.20250507
    1.feat:工时预警优化 

### v1.1.0.34.2025050901
    1.feat:组长群工时预警优化 

### v1.1.0.35.2025051201
    1.feat:绩效系统零星优化需求

### v1.1.0.36.2025051301
    1.fix:处理空指针问题

### v1.1.0.37.2025051401
    1.feat:工作明细增加绩效预警数据TAB页

### v1.1.0.38.2025051402
    1.fix:统计超10天未处理任务数量逻辑修改

### v1.1.0.39.2025052001
    1.fix: bug 组长群工时预警(按天请假统计错误)

### v1.1.0.40.20250521001
    1.feat：生产慢SQL管理

### v1.1.0.41.2025052102
    1.feat：【工作成果跟踪】增加一列“工作时长（加上请假）（小时）”

### v1.1.0.42.2025052701
    1.feat：工时预警需求变更

### v1.1.0.43.2025052801
    1.feat：每月绩效统计排除离职人员

### v1.1.0.44.2025060301
    1.feat：绩效综合统计新增导出功能

### v1.1.0.45.2025060601
    1.feat:慢SQL管理页面需求变更

### v1.1.0.46.2025061001
    1.feat:【慢sql管理】数据变更

### v1.1.0.47.2025061801
    1.feat:绩效系统新增印尼绩效管理模块 

### v1.1.0.48.2025061901
    1.feat:调整免登录跳转逻辑

### v1.1.0.49.2025070701
    1.绩效登记移除离职判断

### v1.1.0.50.2025070901
    1.fix:调整本月请假人员获取逻辑

### v1.1.0.51.2025070901
    1.feat:慢sql和代码质量管理新需求

### v1.1.0.51.2025070902
    1.fix:处理代码查询异常

### v1.1.0.51.2025070903
    1.feat:代码扫描空部门数据不查询出来

### v1.1.0.52.2025071601
    1.feat:绩效指标相关需求（二）

### v1.1.0.53.2025071701
    1.fix: 处理组长绩效反馈定时任务空指针问题

### v1.1.0.54.2025071801
    1.feat: 绩效指标相关需求（二）优化

### v1.1.0.55.2025071801
    1.feat: 组长绩效计算跳过项管审核

### v1.1.0.56.2025072101
    1.feat: 需求用例钉钉提醒调整
    2.feat: 处理代码库查询为空问题

### v1.1.0.57.2025072201
    1.fix:修复慢sql空数据情况
    2.feat:代码扫描需求变动

### v1.1.0.58.2025072301
    1.fix:修复项目管理分页异常

### v1.1.0.59.2025072302
    1.feat:优秀绩效查询统计tab
    2.feat:绩效指标调整 / 月绩效报告增加排序
    3.代码库管理同步数据过滤掉已归档项目

### v1.1.0.60.2025072301
    1.feat:慢SQL增加钉钉提醒

### v1.1.0.61.2025072401
    1.feat:慢sql未指派消息发送到技术中心内部沟通群
    2.feat:慢sql查询增加创建时间和筛选

### v1.1.0.62.2025072401
    1.feat:代码质量管理页添加新增未指派、已指派未处理和已指派已处理的字段。